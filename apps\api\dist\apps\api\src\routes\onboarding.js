"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const asyncHandler_1 = require("../middleware/asyncHandler");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const logger_1 = require("../utils/logger");
const errors_1 = require("../utils/errors");
const userService_1 = require("../services/userService");
const locationService_1 = require("../services/locationService");
const router = (0, express_1.Router)();
// Syrian location validation data
const SYRIAN_LOCATIONS = {
    'دمشق': ['دمشق', 'داريا', 'دوما', 'جرمانا', 'قدسيا'],
    'ريف دمشق': ['الزبداني', 'قطنا', 'التل', 'يبرود', 'النبك'],
    'حلب': ['حلب', 'منبج', 'عفرين', 'اعزاز', 'الباب'],
    'حمص': ['حمص', 'تدمر', 'القريتين', 'الرستن', 'تلبيسة'],
    'حماة': ['حماة', 'سلمية', 'مصياف', 'محردة', 'السقيلبية'],
    'اللاذقية': ['اللاذقية', 'جبلة', 'القرداحة', 'الحفة'],
    'طرطوس': ['طرطوس', 'بانياس', 'صافيتا', 'دريكيش'],
    'إدلب': ['إدلب', 'جسر الشغور', 'أريحا', 'معرة النعمان'],
    'الحسكة': ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية'],
    'دير الزور': ['دير الزور', 'الميادين', 'البوكمال'],
    'الرقة': ['الرقة', 'تل أبيض', 'الثورة'],
    'درعا': ['درعا', 'إزرع', 'الصنمين', 'نوى'],
    'السويداء': ['السويداء', 'شهبا', 'صلخد', 'القريا'],
    'القنيطرة': ['القنيطرة', 'فيق', 'خان أرنبة']
};
const BUSINESS_INDUSTRIES = [
    'التكنولوجيا',
    'التجارة الإلكترونية',
    'التعليم',
    'الصحة',
    'العقارات',
    'السياحة',
    'الصناعة',
    'الزراعة',
    'الخدمات المالية',
    'الإعلام والاتصالات',
    'النقل واللوجستيات',
    'الطاقة',
    'أخرى'
];
const BUSINESS_SIZES = [
    'شركة ناشئة (1-10 موظفين)',
    'شركة صغيرة (11-50 موظف)',
    'شركة متوسطة (51-200 موظف)',
    'شركة كبيرة (200+ موظف)'
];
const SERVICE_CATEGORIES = [
    'تطوير المواقع',
    'التصميم الجرافيكي',
    'التسويق الرقمي',
    'الترجمة',
    'المحاسبة',
    'الاستشارات القانونية',
    'التصوير',
    'الكتابة والتحرير',
    'البرمجة',
    'التدريس',
    'الخدمات الهندسية',
    'الخدمات الطبية',
    'الكهرباء والصيانة',
    'السباكة',
    'النجارة',
    'البناء والتشييد'
];
const PROJECT_TYPES = [
    'مشاريع تقنية',
    'تصميم وإبداع',
    'تسويق ومبيعات',
    'خدمات تجارية',
    'استشارات',
    'خدمات منزلية',
    'خدمات طبية',
    'تعليم وتدريب',
    'خدمات قانونية',
    'خدمات مالية'
];
/**
 * @route POST /api/onboarding/user-data
 * @desc Save user onboarding data
 * @access Private
 */
router.post('/user-data', auth_1.authenticate, [
    (0, express_validator_1.body)('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required'),
    (0, express_validator_1.body)('phoneNumber')
        .matches(/^(\+963|0)?[0-9]{8,9}$/)
        .withMessage('Valid Syrian phone number is required'),
    (0, express_validator_1.body)('location.governorate')
        .isIn(Object.keys(SYRIAN_LOCATIONS))
        .withMessage('Valid Syrian governorate is required'),
    (0, express_validator_1.body)('location.city')
        .custom((value, { req }) => {
        const governorate = req.body.location?.governorate;
        if (!governorate || !SYRIAN_LOCATIONS[governorate]?.includes(value)) {
            throw new Error('Valid city for the selected governorate is required');
        }
        return true;
    }),
    (0, express_validator_1.body)('role')
        .isIn(['EXPERT', 'CLIENT', 'BUSINESS'])
        .withMessage('Valid role is required'),
    (0, express_validator_1.body)('servicePreferences')
        .optional()
        .isArray()
        .custom((value) => {
        if (value && !value.every((item) => SERVICE_CATEGORIES.includes(item))) {
            throw new Error('Invalid service categories');
        }
        return true;
    }),
    (0, express_validator_1.body)('projectTypes')
        .optional()
        .isArray()
        .custom((value) => {
        if (value && !value.every((item) => PROJECT_TYPES.includes(item))) {
            throw new Error('Invalid project types');
        }
        return true;
    }),
    (0, express_validator_1.body)('businessInfo.companyName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Company name must be between 2 and 100 characters'),
    (0, express_validator_1.body)('businessInfo.industry')
        .optional()
        .isIn(BUSINESS_INDUSTRIES)
        .withMessage('Valid business industry is required'),
    (0, express_validator_1.body)('businessInfo.size')
        .optional()
        .isIn(BUSINESS_SIZES)
        .withMessage('Valid business size is required'),
], validation_1.validateRequest, (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { firstName, lastName, email, phoneNumber, location, role, servicePreferences, projectTypes, businessInfo } = req.body;
    const userId = req.user.id;
    logger_1.logger.info('Saving user onboarding data', {
        userId,
        role,
        location: location.governorate,
        hasServicePreferences: !!servicePreferences?.length,
        hasProjectTypes: !!projectTypes?.length,
        hasBusinessInfo: !!businessInfo?.companyName
    });
    // Role-specific validation
    if (role === 'EXPERT' && (!servicePreferences || servicePreferences.length === 0)) {
        throw errors_1.createError.badRequest('Service preferences are required for experts');
    }
    if (role === 'CLIENT' && (!projectTypes || projectTypes.length === 0)) {
        throw errors_1.createError.badRequest('Project types are required for clients');
    }
    if (role === 'BUSINESS') {
        if (!businessInfo?.companyName || !businessInfo?.industry || !businessInfo?.size) {
            throw errors_1.createError.badRequest('Complete business information is required for business accounts');
        }
    }
    try {
        // Save to database using UserService
        await userService_1.UserService.saveOnboardingData({
            userId,
            firstName,
            lastName,
            email,
            phoneNumber,
            governorate: location.governorate,
            city: location.city,
            role,
            servicePreferences,
            projectTypes,
            businessInfo
        });
        // If expert, add default service area for their location
        if (role === 'EXPERT') {
            try {
                // Get expert profile ID first
                const userProfile = await userService_1.UserService.getUserData(userId);
                if (userProfile) {
                    await locationService_1.LocationService.addExpertServiceArea(userId, // Using userId as expertId for now
                    location.governorate, location.city, 0, // Default service radius (city only)
                    0.5 // Default travel cost per km
                    );
                }
            }
            catch (locationError) {
                // Log but don't fail the onboarding if service area creation fails
                logger_1.logger.warn('Failed to create default service area for expert', {
                    userId,
                    error: locationError instanceof Error ? locationError.message : 'Unknown error'
                });
            }
        }
        logger_1.logger.info('User onboarding data saved successfully', {
            userId,
            role,
            location: `${location.city}, ${location.governorate}`
        });
        res.status(200).json({
            success: true,
            message: 'User data saved successfully',
            data: {
                userId,
                dataCollected: true,
                nextStep: 'ai_introduction',
                location: {
                    governorate: location.governorate,
                    city: location.city
                },
                role
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error saving user onboarding data', {
            userId,
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        });
        throw errors_1.createError.internalServerError('Failed to save user data');
    }
}));
/**
 * @route GET /api/onboarding/user-data/:userId
 * @desc Get user onboarding data
 * @access Private
 */
router.get('/user-data/:userId', auth_1.authenticate, (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { userId } = req.params;
    const requestingUserId = req.user.id;
    // Users can only access their own data (or admins can access any)
    if (userId !== requestingUserId && req.user.role !== 'ADMIN') {
        throw errors_1.createError.forbidden('Access denied');
    }
    try {
        // Fetch from database using UserService
        const userData = await userService_1.UserService.getUserData(userId);
        if (!userData) {
            throw errors_1.createError.notFound('User not found');
        }
        res.status(200).json({
            success: true,
            data: {
                userId: userData.id,
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email,
                phoneNumber: userData.phoneNumber,
                governorate: userData.governorate,
                city: userData.city,
                role: userData.role,
                servicePreferences: userData.servicePreferences,
                projectTypes: userData.projectTypes,
                businessInfo: userData.businessInfo,
                dataCollected: userData.dataCollected,
                hasCompletedOnboarding: userData.hasCompletedOnboarding,
                collectedAt: userData.updatedAt
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching user onboarding data', {
            userId,
            requestingUserId,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw errors_1.createError.internalServerError('Failed to fetch user data');
    }
}));
/**
 * @route GET /api/onboarding/locations/governorates
 * @desc Get all Syrian governorates
 * @access Public
 */
router.get('/locations/governorates', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const governorates = Object.keys(SYRIAN_LOCATIONS).map(name => ({
        name,
        cities: SYRIAN_LOCATIONS[name]
    }));
    res.status(200).json({
        success: true,
        data: governorates
    });
}));
/**
 * @route GET /api/onboarding/locations/cities/:governorate
 * @desc Get cities for a specific governorate
 * @access Public
 */
router.get('/locations/cities/:governorate', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { governorate } = req.params;
    const cities = SYRIAN_LOCATIONS[governorate];
    if (!cities) {
        throw errors_1.createError.notFound('Governorate not found');
    }
    res.status(200).json({
        success: true,
        data: {
            governorate,
            cities
        }
    });
}));
/**
 * @route GET /api/onboarding/experts/search
 * @desc Search experts by location and services
 * @access Public
 */
router.get('/experts/search', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { governorate, city, serviceCategories, page = 1, limit = 20 } = req.query;
    logger_1.logger.info('Searching experts by location', {
        governorate,
        city,
        serviceCategories,
        page,
        limit
    });
    try {
        const searchParams = {
            governorate: governorate,
            city: city,
            serviceCategories: serviceCategories ?
                (Array.isArray(serviceCategories) ? serviceCategories : [serviceCategories]) :
                undefined,
            page: parseInt(page),
            limit: parseInt(limit)
        };
        const results = await locationService_1.LocationService.searchExpertsByLocation(searchParams);
        res.status(200).json({
            success: true,
            data: results
        });
    }
    catch (error) {
        logger_1.logger.error('Error searching experts by location', {
            error: error instanceof Error ? error.message : 'Unknown error',
            query: req.query
        });
        throw errors_1.createError.internalServerError('Failed to search experts');
    }
}));
/**
 * @route POST /api/onboarding/experts/:expertId/service-areas
 * @desc Add service area for an expert
 * @access Private (Expert only)
 */
router.post('/experts/:expertId/service-areas', auth_1.authenticate, [
    (0, express_validator_1.body)('governorate')
        .isIn(Object.keys(SYRIAN_LOCATIONS))
        .withMessage('Valid Syrian governorate is required'),
    (0, express_validator_1.body)('city')
        .custom((value, { req }) => {
        const governorate = req.body.governorate;
        if (!governorate || !SYRIAN_LOCATIONS[governorate]?.includes(value)) {
            throw new Error('Valid city for the selected governorate is required');
        }
        return true;
    }),
    (0, express_validator_1.body)('serviceRadius')
        .optional()
        .isInt({ min: 0, max: 100 })
        .withMessage('Service radius must be between 0 and 100 km'),
    (0, express_validator_1.body)('travelCost')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Travel cost must be a positive number')
], validation_1.validateRequest, (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { expertId } = req.params;
    const { governorate, city, serviceRadius = 0, travelCost } = req.body;
    const userId = req.user.id;
    // Ensure user can only manage their own service areas
    if (expertId !== userId && req.user.role !== 'ADMIN') {
        throw errors_1.createError.forbidden('Access denied');
    }
    try {
        const serviceArea = await locationService_1.LocationService.addExpertServiceArea(expertId, governorate, city, serviceRadius, travelCost);
        res.status(201).json({
            success: true,
            message: 'Service area added successfully',
            data: serviceArea
        });
    }
    catch (error) {
        logger_1.logger.error('Error adding expert service area', {
            expertId,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
    }
}));
/**
 * @route GET /api/onboarding/experts/:expertId/service-areas
 * @desc Get expert service areas
 * @access Private
 */
router.get('/experts/:expertId/service-areas', auth_1.authenticate, (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { expertId } = req.params;
    const userId = req.user.id;
    // Ensure user can only access their own service areas (or admins can access any)
    if (expertId !== userId && req.user.role !== 'ADMIN') {
        throw errors_1.createError.forbidden('Access denied');
    }
    try {
        const serviceAreas = await locationService_1.LocationService.getExpertServiceAreas(expertId);
        res.status(200).json({
            success: true,
            data: serviceAreas
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching expert service areas', {
            expertId,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
    }
}));
exports.default = router;
//# sourceMappingURL=onboarding.js.map