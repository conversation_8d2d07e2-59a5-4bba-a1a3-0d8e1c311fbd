/**
 * Test AI Endpoints for Freela Syria
 */

const axios = require('axios');

const API_URL = 'http://localhost:3001';

async function testAIEndpoints() {
  console.log('🤖 Testing AI Endpoints for Freela Syria\n');

  try {
    // Test AI conversation start
    console.log('1️⃣ Testing AI conversation start...');
    const startResponse = await axios.post(`${API_URL}/api/v1/ai/conversation/start`, {
      userRole: 'EXPERT',
      language: 'ar',
      sessionType: 'onboarding'
    });

    console.log('✅ AI Conversation Start Response:');
    console.log('   Session ID:', startResponse.data.data.sessionId);
    console.log('   Welcome Message:', startResponse.data.data.messages[0].content.substring(0, 50) + '...');
    console.log('   Status:', startResponse.data.data.status);

    const sessionId = startResponse.data.data.sessionId;

    // Test AI conversation message
    console.log('\n2️⃣ Testing AI conversation message...');
    const messageResponse = await axios.post(`${API_URL}/api/v1/ai/conversation/message`, {
      sessionId: sessionId,
      message: 'أنا مطور ويب متخصص في React و Node.js',
      messageType: 'user'
    });

    console.log('✅ AI Message Response:');
    console.log('   AI Reply:', messageResponse.data.data.aiMessage.content.substring(0, 50) + '...');
    console.log('   Completion Rate:', messageResponse.data.data.completionRate);
    console.log('   Extracted Skills:', messageResponse.data.data.extractedData.skills);

    console.log('\n🎉 AI Endpoints Test Results:');
    console.log('   ✅ AI conversation start working');
    console.log('   ✅ AI message processing working');
    console.log('   ✅ Arabic language support confirmed');
    console.log('   ✅ Session management functional');
    console.log('   ✅ Data extraction working');

  } catch (error) {
    console.log('❌ AI Endpoints Error:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

// Run the test
testAIEndpoints().catch(console.error);
