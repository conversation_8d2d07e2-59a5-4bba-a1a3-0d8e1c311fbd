// Freela Syria Database Schema
generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  CLIENT
  EXPERT
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum ServiceStatus {
  DRAFT
  PENDING_REVIEW
  ACTIVE
  PAUSED
  REJECTED
  ARCHIVED
}

enum BookingStatus {
  PENDING
  ACCEPTED
  IN_PROGRESS
  DELIVERED
  REVISION_REQUESTED
  COMPLETED
  CANCELLED
  DISPUTED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  DISPUTED
  CHARGEBACK
}

enum PaymentMethod {
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  BANK_TRANSFER
  MOBILE_WALLET
  CRYPTOCURRENCY
  CASH
}

enum NotificationType {
  BOOKING_REQUEST
  BOOKING_ACCEPTED
  BOOKING_REJECTED
  BOOKING_COMPLETED
  PAYMENT_RECEIVED
  MESSAGE_RECEIVED
  PROFILE_APPROVED
  SERVICE_APPROVED
  SYSTEM_ANNOUNCEMENT
}

// Core Models
model User {
  id              String     @id @default(cuid())
  email           String     @unique
  phone           String?
  firstName       String
  lastName        String
  avatar          Json?      // FileUpload object
  role            UserRole
  status          UserStatus @default(ACTIVE)
  language        String     @default("ar")
  location        Json?      // Location object
  emailVerified   Boolean    @default(false)
  phoneVerified   Boolean    @default(false)
  emailVerificationToken String?
  passwordHash    String
  provider        String?    // OAuth provider (GOOGLE, FACEBOOK, etc.)
  providerId      String?    // OAuth provider user ID
  hasCompletedOnboarding Boolean @default(false) // AI onboarding completion status

  // Enhanced data collection fields
  governorate     String?    // Syrian governorate
  city            String?    // Syrian city
  phoneNumber     String?    // Formatted phone number
  dataCollected   Boolean    @default(false) // Data collection completion status
  servicePreferences String[] // For experts - preferred service categories
  projectTypes    String[]   // For clients - preferred project types
  businessInfo    Json?      // For business accounts - company details

  lastLoginAt     DateTime?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Profile relations
  expertProfile   ExpertProfile?
  clientProfile   ClientProfile?

  // Activity relations
  sentMessages    Message[]       @relation("MessageSender")
  receivedMessages Message[]      @relation("MessageReceiver")
  notifications   Notification[]
  sessions        UserSession[]
  
  // Booking relations
  clientBookings  Booking[]       @relation("ClientBookings")
  expertBookings  Booking[]       @relation("ExpertBookings")
  
  // Payment relations
  payments        Payment[]       @relation("PaymentPayer")
  receivedPayments Payment[]      @relation("PaymentPayee")
  
  // Review relations
  givenReviews    Review[]        @relation("ReviewGiver")
  receivedReviews Review[]        @relation("ReviewReceiver")

  // AI Onboarding relations
  aiConversationSessions AIConversationSession[] @relation("AIConversationUser")
  aiRecommendations      AIRecommendation[]      @relation("AIRecommendationUser")

  @@index([email])
  @@index([role])
  @@index([status])
  @@map("users")
}

model ExpertProfile {
  id                    String    @id @default(cuid())
  userId                String    @unique
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  title                 Json      // LocalizedString
  description           Json      // LocalizedString
  skills                String[]
  experience            String    // ExperienceLevel
  hourlyRate            Float?
  availability          Json      // Availability object
  responseTime          String    // ResponseTime
  completedProjects     Int       @default(0)
  rating                Float     @default(0)
  reviewCount           Int       @default(0)
  verified              Boolean   @default(false)
  verificationDocuments Json[]    // FileUpload[]
  
  // Relations
  services              Service[]
  education             Education[]
  certifications        Certification[]
  portfolio             PortfolioItem[]
  languages             LanguageSkill[]
  serviceAreas          ExpertServiceArea[]

  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@map("expert_profiles")
}

model ClientProfile {
  id            String    @id @default(cuid())
  userId        String    @unique
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  companyName   String?
  companySize   String?   // CompanySize
  industry      String?
  projectsPosted Int      @default(0)
  totalSpent    Float     @default(0)
  rating        Float     @default(0)
  reviewCount   Int       @default(0)
  
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("client_profiles")
}

model Service {
  id            String        @id @default(cuid())
  expertId      String
  expert        ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)
  
  title         Json          // LocalizedString
  description   Json          // LocalizedString
  categoryId    String
  category      ServiceCategory @relation(fields: [categoryId], references: [id])
  subcategory   String?
  tags          String[]
  images        Json[]        // FileUpload[]
  pricing       Json          // ServicePricing object
  deliveryTime  Int           // in days
  revisions     Int
  requirements  Json[]        // ServiceRequirement[]
  addOns        Json[]        // ServiceAddOn[]
  status        ServiceStatus @default(DRAFT)
  featured      Boolean       @default(false)
  rating        Float         @default(0)
  reviewCount   Int           @default(0)
  orderCount    Int           @default(0)
  lastOrderAt   DateTime?
  seoSlug       String        @unique
  metadata      Json?         // ServiceMetadata object
  
  // Relations
  bookings      Booking[]
  reviews       Review[]
  
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@index([status])
  @@index([categoryId])
  @@index([expertId])
  @@map("services")
}

model ServiceCategory {
  id            String    @id @default(cuid())
  name          Json      // LocalizedString
  slug          String    @unique
  description   Json?     // LocalizedString
  icon          String?
  parentId      String?
  parent        ServiceCategory? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  subcategories ServiceCategory[] @relation("CategoryHierarchy")
  serviceCount  Int       @default(0)
  featured      Boolean   @default(false)
  
  // Relations
  services      Service[]
  
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("service_categories")
}

model Booking {
  id            String        @id @default(cuid())
  clientId      String
  client        User          @relation("ClientBookings", fields: [clientId], references: [id])
  expertId      String
  expert        User          @relation("ExpertBookings", fields: [expertId], references: [id])
  serviceId     String
  service       Service       @relation(fields: [serviceId], references: [id])

  packageId     String?
  status        BookingStatus @default(PENDING)
  pricing       Json          // BookingPricing object
  timeline      Json          // BookingTimeline object
  requirements  Json[]        // BookingRequirement[]
  addOns        Json[]        // BookingAddOn[]
  milestones    Json[]        // Milestone[]
  cancellation  Json?         // BookingCancellation object
  completion    Json?         // BookingCompletion object
  metadata      Json          // BookingMetadata object

  // Relations
  deliverables  Deliverable[]
  revisions     Revision[]
  messages      Message[]
  payments      Payment[]
  reviews       Review[]
  disputes      Dispute[]

  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@index([status])
  @@index([clientId])
  @@index([expertId])
  @@index([serviceId])
  @@map("bookings")
}

model Deliverable {
  id            String    @id @default(cuid())
  bookingId     String
  booking       Booking   @relation(fields: [bookingId], references: [id], onDelete: Cascade)

  title         Json      // LocalizedString
  description   Json?     // LocalizedString
  files         Json[]    // FileUpload[]
  type          String    // DeliverableType
  status        String    // DeliverableStatus
  submittedBy   String    // 'expert' | 'client'
  revisionNumber Int      @default(0)
  feedback      Json?     // LocalizedString

  // Relations
  revisions     Revision[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("deliverables")
}

model Revision {
  id            String      @id @default(cuid())
  bookingId     String
  booking       Booking     @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  deliverableId String
  deliverable   Deliverable @relation(fields: [deliverableId], references: [id], onDelete: Cascade)

  requestedBy   String      // client ID
  reason        Json        // LocalizedString
  instructions  Json        // LocalizedString
  files         Json[]      // FileUpload[]
  status        String      // RevisionStatus
  response      Json?       // Response object

  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("revisions")
}

model Payment {
  id                    String        @id @default(cuid())
  bookingId             String
  booking               Booking       @relation(fields: [bookingId], references: [id])
  payerId               String
  payer                 User          @relation("PaymentPayer", fields: [payerId], references: [id])
  payeeId               String
  payee                 User          @relation("PaymentPayee", fields: [payeeId], references: [id])

  amount                Float
  currency              String        @default("USD")
  status                PaymentStatus @default(PENDING)
  method                PaymentMethod
  gateway               String        // PaymentGateway
  gatewayTransactionId  String?
  gatewayResponse       Json?
  fees                  Json          // PaymentFees object
  escrow                Json          // EscrowDetails object
  refund                Json?         // RefundDetails object
  metadata              Json          // PaymentMetadata object

  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt

  @@index([status])
  @@index([bookingId])
  @@map("payments")
}

model Message {
  id              String    @id @default(cuid())
  conversationId  String
  conversation    Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  senderId        String
  sender          User      @relation("MessageSender", fields: [senderId], references: [id])
  receiverId      String?
  receiver        User?     @relation("MessageReceiver", fields: [receiverId], references: [id])
  bookingId       String?
  booking         Booking?  @relation(fields: [bookingId], references: [id])

  content         Json      // MessageContent object
  type            String    // MessageType
  status          String    // MessageStatus
  replyTo         String?   // Message ID
  reactions       Json[]    // MessageReaction[]
  editHistory     Json[]    // MessageEdit[]
  metadata        Json      // MessageMetadata object

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([conversationId])
  @@index([senderId])
  @@map("messages")
}

model Conversation {
  id            String    @id @default(cuid())
  participants  Json[]    // ChatParticipant[]
  type          String    // ConversationType
  bookingId     String?
  title         Json?     // LocalizedString
  lastMessageId String?
  unreadCount   Json      // Record<string, number>
  status        String    // ConversationStatus
  metadata      Json      // ConversationMetadata object

  // Relations
  messages      Message[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("conversations")
}

model Review {
  id              String    @id @default(cuid())
  serviceId       String
  service         Service   @relation(fields: [serviceId], references: [id])
  bookingId       String
  booking         Booking   @relation(fields: [bookingId], references: [id])
  giverId         String
  giver           User      @relation("ReviewGiver", fields: [giverId], references: [id])
  receiverId      String
  receiver        User      @relation("ReviewReceiver", fields: [receiverId], references: [id])

  rating          Int       // 1-5
  title           Json?     // LocalizedString
  comment         Json?     // LocalizedString
  pros            Json[]    // LocalizedString[]
  cons            Json[]    // LocalizedString[]
  wouldRecommend  Boolean   @default(true)
  helpful         Int       @default(0)
  reported        Boolean   @default(false)
  expertResponse  Json?     // Expert response object

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@map("reviews")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  type      NotificationType
  title     Json             // LocalizedString
  message   Json             // LocalizedString
  data      Json?            // Additional data
  read      Boolean          @default(false)

  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@index([userId])
  @@index([read])
  @@map("notifications")
}

model UserSession {
  id            String    @id @default(cuid())
  userId        String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  token         String    @unique
  refreshToken  String?   @unique
  expiresAt     DateTime
  deviceInfo    Json?     // Device information
  ipAddress     String?
  userAgent     String?
  active        Boolean   @default(true)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("user_sessions")
}

model Education {
  id            String        @id @default(cuid())
  expertId      String
  expert        ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)

  institution   String
  degree        String
  field         String
  startYear     Int
  endYear       Int?
  current       Boolean       @default(false)
  description   String?

  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("education")
}

model Certification {
  id            String        @id @default(cuid())
  expertId      String
  expert        ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)

  name          String
  issuer        String
  issueDate     DateTime
  expiryDate    DateTime?
  credentialId  String?
  credentialUrl String?
  verified      Boolean       @default(false)

  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("certifications")
}

model PortfolioItem {
  id            String        @id @default(cuid())
  expertId      String
  expert        ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)

  title         Json          // LocalizedString
  description   Json          // LocalizedString
  images        Json[]        // FileUpload[]
  category      String
  tags          String[]
  projectUrl    String?
  completedAt   DateTime
  featured      Boolean       @default(false)

  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("portfolio_items")
}

model LanguageSkill {
  id            String        @id @default(cuid())
  expertId      String
  expert        ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)

  language      String
  proficiency   String        // LanguageProficiency
  native        Boolean       @default(false)

  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("language_skills")
}

// Expert Service Areas for Location-Based Matching
model ExpertServiceArea {
  id            String        @id @default(cuid())
  expertId      String
  expert        ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)

  governorate   String        // Syrian governorate
  city          String        // Syrian city
  serviceRadius Int           @default(0) // Service radius in kilometers (0 = city only)
  travelCost    Float?        // Cost per kilometer for travel
  isActive      Boolean       @default(true)

  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@unique([expertId, governorate, city])
  @@index([governorate])
  @@index([city])
  @@map("expert_service_areas")
}

model Dispute {
  id            String    @id @default(cuid())
  bookingId     String
  booking       Booking   @relation(fields: [bookingId], references: [id])

  initiatedBy   String    // User ID
  reason        String    // DisputeReason
  description   Json      // LocalizedString
  evidence      Json[]    // FileUpload[]
  status        String    // DisputeStatus
  resolution    Json?     // DisputeResolution object
  assignedTo    String?   // Admin ID
  messages      Json[]    // DisputeMessage[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("disputes")
}

model SystemSettings {
  id            String    @id @default(cuid())
  key           String    @unique
  value         Json      // Any JSON value
  description   String?
  category      String?
  public        Boolean   @default(false)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("system_settings")
}

model AuditLog {
  id            String    @id @default(cuid())
  userId        String?   // User who performed the action
  action        String    // Action performed
  entityType    String    // Type of entity affected
  entityId      String?   // ID of entity affected
  oldValues     Json?     // Previous values
  newValues     Json?     // New values
  ipAddress     String?
  userAgent     String?
  metadata      Json?     // Additional context

  createdAt     DateTime  @default(now())

  @@map("audit_logs")
}

model FileUpload {
  id            String    @id @default(cuid())
  filename      String
  originalName  String
  mimeType      String
  size          Int
  url           String
  thumbnailUrl  String?
  uploadedBy    String?   // User ID
  entityType    String?   // What this file is attached to
  entityId      String?   // ID of the entity
  metadata      Json?     // Additional file metadata

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("file_uploads")
}

model Analytics {
  id            String    @id @default(cuid())
  metric        String    // Metric name
  value         Float     // Metric value
  dimensions    Json      // Dimensions/filters
  timestamp     DateTime  // When the metric was recorded
  period        String?   // Time period (hour, day, week, etc.)

  createdAt     DateTime  @default(now())

  @@index([metric, timestamp])
  @@index([timestamp])
  @@map("analytics")
}

// AI Onboarding System Models
model AIConversationSession {
  id                String    @id @default(cuid())
  userId            String
  user              User      @relation("AIConversationUser", fields: [userId], references: [id], onDelete: Cascade)

  sessionType       String    // 'onboarding', 'profile_optimization', 'service_creation'
  userRole          UserRole  // CLIENT or EXPERT
  language          String    @default("ar")
  currentStep       String    // Current step in the conversation flow
  status            String    @default("active") // active, completed, abandoned, paused

  // Session Configuration
  aiModel           String    @default("openai/gpt-4-turbo-preview")
  temperature       Float     @default(0.7)
  maxTokens         Int       @default(1000)

  // Progress Tracking
  completionRate    Float     @default(0.0) // 0.0 to 1.0
  stepsCompleted    String[]  // Array of completed step names
  totalSteps        Int       @default(0)

  // Extracted Data Storage
  extractedData     Json      @default("{}")
  profileData       Json?     // Generated profile data
  serviceData       Json?     // Generated service data
  recommendations   Json[]    // AI recommendations

  // Session Metadata
  startedAt         DateTime  @default(now())
  lastActiveAt      DateTime  @default(now())
  completedAt       DateTime?
  estimatedDuration Int?      // Estimated duration in minutes
  actualDuration    Int?      // Actual duration in minutes

  // Relations
  messages          AIConversationMessage[]
  extractedEntities AIExtractedData[]
  recommendations_rel AIRecommendation[]

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([userId])
  @@index([status])
  @@index([sessionType])
  @@map("ai_conversation_sessions")
}

model AIConversationMessage {
  id                String                @id @default(cuid())
  sessionId         String
  session           AIConversationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  role              String                // 'system', 'user', 'assistant'
  content           String                // Message content
  contentArabic     String?               // Arabic translation if needed

  // Message Metadata
  messageType       String                @default("text") // text, image, file, structured_data
  stepName          String?               // Which step this message belongs to
  intent            String?               // Detected user intent
  confidence        Float?                // AI confidence score

  // AI Processing
  aiModel           String?               // Model used to generate this message
  promptTokens      Int?                  // Tokens used in prompt
  completionTokens  Int?                  // Tokens used in completion
  totalTokens       Int?                  // Total tokens used
  processingTime    Int?                  // Processing time in milliseconds

  // User Interaction
  userFeedback      String?               // User feedback on AI response
  userRating        Int?                  // 1-5 rating
  flagged           Boolean               @default(false)
  flagReason        String?

  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  @@index([sessionId])
  @@index([role])
  @@index([createdAt])
  @@map("ai_conversation_messages")
}

model AIExtractedData {
  id                String                @id @default(cuid())
  sessionId         String
  session           AIConversationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  // Data Classification
  dataType          String                // 'skill', 'experience', 'preference', 'requirement', etc.
  category          String                // Specific category within the data type
  subcategory       String?               // More specific classification

  // Extracted Information
  originalText      String                // Original user input
  extractedValue    Json                  // Structured extracted data
  normalizedValue   String?               // Normalized/standardized value
  confidence        Float                 // AI confidence in extraction (0.0-1.0)

  // Validation
  validated         Boolean               @default(false)
  validatedBy       String?               // User ID who validated
  validatedAt       DateTime?
  validationNotes   String?

  // Processing Metadata
  extractionMethod  String                // 'ai_nlp', 'pattern_matching', 'user_selection'
  aiModel           String?               // Model used for extraction
  processingVersion String                @default("1.0")

  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  @@index([sessionId])
  @@index([dataType])
  @@index([validated])
  @@map("ai_extracted_data")
}

model AIRecommendation {
  id                String                @id @default(cuid())
  sessionId         String
  session           AIConversationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  userId            String
  user              User                  @relation("AIRecommendationUser", fields: [userId], references: [id])

  // Recommendation Details
  type              String                // 'profile_improvement', 'service_suggestion', 'pricing', 'market_opportunity'
  category          String                // Specific category within the type
  priority          String                @default("medium") // low, medium, high, critical

  // Recommendation Content
  title             String
  titleArabic       String?
  description       String
  descriptionArabic String?
  recommendationData Json                 // Structured recommendation data
  actionRequired    Json?                 // Specific actions user should take
  expectedImpact    String?               // What improvement to expect

  // AI Analysis
  confidenceScore   Float                 // AI confidence in recommendation (0.0-1.0)
  marketAnalysis    Json?                 // Supporting market data
  competitorAnalysis Json?                // How this compares to competitors
  successProbability Float?               // Likelihood of positive outcome (0.0-1.0)

  // User Interaction
  status            String                @default("pending") // pending, viewed, accepted, rejected, implemented, expired
  userFeedback      String?
  userRating        Int?                  // 1-5 rating
  implementedAt     DateTime?

  // Tracking
  viewedAt          DateTime?
  respondedAt       DateTime?
  expiresAt         DateTime?

  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([priority])
  @@map("ai_recommendations")
}

model AIMarketIntelligence {
  id                String    @id @default(cuid())

  // Market Data
  category          String    // Service category
  subcategory       String?   // Service subcategory
  region            String    @default("syria") // Geographic region

  // Pricing Intelligence
  averagePrice      Float?    // Average price for this service
  priceRange        Json?     // Min/max price range
  pricingModel      String?   // hourly, fixed, package
  currency          String    @default("USD")

  // Demand Analysis
  demandLevel       String?   // low, medium, high, very_high
  demandTrend       String?   // increasing, stable, decreasing
  seasonality       Json?     // Seasonal demand patterns

  // Competition Analysis
  competitorCount   Int?      // Number of active competitors
  competitionLevel  String?   // low, medium, high, saturated
  topSkills         String[]  // Most in-demand skills

  // Market Opportunities
  opportunities     Json[]    // Identified market opportunities
  threats           Json[]    // Market threats or challenges
  recommendations   Json[]    // Strategic recommendations

  // Data Sources and Quality
  dataSource        String    // Source of the intelligence data
  dataQuality       Float     @default(0.8) // Quality score (0.0-1.0)
  lastUpdated       DateTime  @default(now())
  validUntil        DateTime? // When this data expires

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([category])
  @@index([region])
  @@index([lastUpdated])
  @@map("ai_market_intelligence")
}

// Note: Indexes are defined within each model using @@index directive
