import { supabase } from '@freela/database';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';

export interface UserOnboardingData {
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  governorate: string;
  city: string;
  role: 'EXPERT' | 'CLIENT' | 'BUSINESS';
  servicePreferences?: string[];
  projectTypes?: string[];
  businessInfo?: {
    companyName: string;
    industry: string;
    size: string;
  };
}

export interface UserProfile {
  id: string;
  email: string;
  phone?: string;
  firstName: string;
  lastName: string;
  role: string;
  governorate?: string;
  city?: string;
  phoneNumber?: string;
  dataCollected: boolean;
  servicePreferences?: string[];
  projectTypes?: string[];
  businessInfo?: any;
  hasCompletedOnboarding: boolean;
  createdAt: string;
  updatedAt: string;
}

export class UserService {
  /**
   * Save user onboarding data to database
   */
  static async saveOnboardingData(data: UserOnboardingData): Promise<void> {
    try {
      logger.info('Saving user onboarding data to database', {
        userId: data.userId,
        role: data.role,
        location: `${data.city}, ${data.governorate}`
      });

      // Update user profile with onboarding data
      const { error: userError } = await supabase
        .from('users')
        .update({
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
          phone_number: data.phoneNumber,
          governorate: data.governorate,
          city: data.city,
          role: data.role,
          service_preferences: data.servicePreferences || [],
          project_types: data.projectTypes || [],
          business_info: data.businessInfo || null,
          data_collected: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.userId);

      if (userError) {
        logger.error('Error updating user profile', {
          userId: data.userId,
          error: userError.message
        });
        throw createError.internalServerError('Failed to save user data');
      }

      // Create role-specific profile if needed
      if (data.role === 'EXPERT') {
        await this.createExpertProfile(data.userId, data.servicePreferences || []);
      } else if (data.role === 'CLIENT') {
        await this.createClientProfile(data.userId);
      }

      logger.info('User onboarding data saved successfully', {
        userId: data.userId,
        role: data.role
      });

    } catch (error) {
      logger.error('Error in saveOnboardingData', {
        userId: data.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get user onboarding data
   */
  static async getUserData(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          phone,
          first_name,
          last_name,
          role,
          governorate,
          city,
          phone_number,
          data_collected,
          has_completed_onboarding,
          service_preferences,
          project_types,
          business_info,
          created_at,
          updated_at
        `)
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // User not found
        }
        logger.error('Error fetching user data', {
          userId,
          error: error.message
        });
        throw createError.internalServerError('Failed to fetch user data');
      }

      return {
        id: data.id,
        email: data.email,
        phone: data.phone,
        firstName: data.first_name,
        lastName: data.last_name,
        role: data.role,
        governorate: data.governorate,
        city: data.city,
        phoneNumber: data.phone_number,
        dataCollected: data.data_collected,
        servicePreferences: data.service_preferences,
        projectTypes: data.project_types,
        businessInfo: data.business_info,
        hasCompletedOnboarding: data.has_completed_onboarding,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

    } catch (error) {
      logger.error('Error in getUserData', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Create expert profile
   */
  private static async createExpertProfile(userId: string, skills: string[]): Promise<void> {
    try {
      // Check if expert profile already exists
      const { data: existingProfile } = await supabase
        .from('expert_profiles')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (existingProfile) {
        logger.info('Expert profile already exists', { userId });
        return;
      }

      // Create new expert profile
      const { error } = await supabase
        .from('expert_profiles')
        .insert({
          user_id: userId,
          title: { ar: 'خبير جديد', en: 'New Expert' },
          description: { ar: 'خبير في منصة فريلا سوريا', en: 'Expert on Freela Syria platform' },
          skills: skills,
          experience: 'BEGINNER',
          response_time: 'WITHIN_24_HOURS',
          availability: {
            timezone: 'Asia/Damascus',
            workingHours: {
              monday: { start: '09:00', end: '17:00', available: true },
              tuesday: { start: '09:00', end: '17:00', available: true },
              wednesday: { start: '09:00', end: '17:00', available: true },
              thursday: { start: '09:00', end: '17:00', available: true },
              friday: { start: '09:00', end: '17:00', available: true },
              saturday: { start: '09:00', end: '17:00', available: false },
              sunday: { start: '09:00', end: '17:00', available: false }
            }
          }
        });

      if (error) {
        logger.error('Error creating expert profile', {
          userId,
          error: error.message
        });
        throw createError.internalServerError('Failed to create expert profile');
      }

      logger.info('Expert profile created successfully', { userId });

    } catch (error) {
      logger.error('Error in createExpertProfile', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Create client profile
   */
  private static async createClientProfile(userId: string): Promise<void> {
    try {
      // Check if client profile already exists
      const { data: existingProfile } = await supabase
        .from('client_profiles')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (existingProfile) {
        logger.info('Client profile already exists', { userId });
        return;
      }

      // Create new client profile
      const { error } = await supabase
        .from('client_profiles')
        .insert({
          user_id: userId
        });

      if (error) {
        logger.error('Error creating client profile', {
          userId,
          error: error.message
        });
        throw createError.internalServerError('Failed to create client profile');
      }

      logger.info('Client profile created successfully', { userId });

    } catch (error) {
      logger.error('Error in createClientProfile', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update user onboarding completion status
   */
  static async markOnboardingComplete(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          has_completed_onboarding: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        logger.error('Error marking onboarding complete', {
          userId,
          error: error.message
        });
        throw createError.internalServerError('Failed to update onboarding status');
      }

      logger.info('Onboarding marked as complete', { userId });

    } catch (error) {
      logger.error('Error in markOnboardingComplete', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
