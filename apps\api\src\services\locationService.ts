import { supabase } from '@freela/database';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';

export interface ExpertServiceArea {
  id: string;
  expertId: string;
  governorate: string;
  city: string;
  serviceRadius: number;
  travelCost?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LocationSearchParams {
  governorate?: string;
  city?: string;
  serviceCategories?: string[];
  maxDistance?: number;
  page?: number;
  limit?: number;
}

export interface ExpertLocationMatch {
  expertId: string;
  expertProfile: {
    id: string;
    title: any;
    description: any;
    skills: string[];
    rating: number;
    reviewCount: number;
    hourlyRate?: number;
    responseTime: string;
    user: {
      firstName: string;
      lastName: string;
      avatar?: any;
    };
  };
  serviceArea: ExpertServiceArea;
  distance?: number;
  estimatedTravelCost?: number;
}

export class LocationService {
  /**
   * Add service area for an expert
   */
  static async addExpertServiceArea(
    expertId: string,
    governorate: string,
    city: string,
    serviceRadius: number = 0,
    travelCost?: number
  ): Promise<ExpertServiceArea> {
    try {
      logger.info('Adding expert service area', {
        expertId,
        location: `${city}, ${governorate}`,
        serviceRadius
      });

      const { data, error } = await supabase
        .from('expert_service_areas')
        .insert({
          expert_id: expertId,
          governorate,
          city,
          service_radius: serviceRadius,
          travel_cost: travelCost,
          is_active: true
        })
        .select()
        .single();

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          throw createError.conflict('Service area already exists for this location');
        }
        logger.error('Error adding expert service area', {
          expertId,
          error: error.message
        });
        throw createError.internalServerError('Failed to add service area');
      }

      return {
        id: data.id,
        expertId: data.expert_id,
        governorate: data.governorate,
        city: data.city,
        serviceRadius: data.service_radius,
        travelCost: data.travel_cost,
        isActive: data.is_active,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

    } catch (error) {
      logger.error('Error in addExpertServiceArea', {
        expertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get expert service areas
   */
  static async getExpertServiceAreas(expertId: string): Promise<ExpertServiceArea[]> {
    try {
      const { data, error } = await supabase
        .from('expert_service_areas')
        .select('*')
        .eq('expert_id', expertId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error fetching expert service areas', {
          expertId,
          error: error.message
        });
        throw createError.internalServerError('Failed to fetch service areas');
      }

      return data.map(area => ({
        id: area.id,
        expertId: area.expert_id,
        governorate: area.governorate,
        city: area.city,
        serviceRadius: area.service_radius,
        travelCost: area.travel_cost,
        isActive: area.is_active,
        createdAt: area.created_at,
        updatedAt: area.updated_at
      }));

    } catch (error) {
      logger.error('Error in getExpertServiceAreas', {
        expertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Search experts by location
   */
  static async searchExpertsByLocation(params: LocationSearchParams): Promise<{
    experts: ExpertLocationMatch[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const {
        governorate,
        city,
        serviceCategories = [],
        page = 1,
        limit = 20
      } = params;

      logger.info('Searching experts by location', {
        governorate,
        city,
        serviceCategories,
        page,
        limit
      });

      // Build the query
      let query = supabase
        .from('expert_service_areas')
        .select(`
          *,
          expert_profiles!inner (
            id,
            title,
            description,
            skills,
            rating,
            review_count,
            hourly_rate,
            response_time,
            users!inner (
              first_name,
              last_name,
              avatar
            )
          )
        `)
        .eq('is_active', true);

      // Add location filters
      if (governorate) {
        query = query.eq('governorate', governorate);
      }
      if (city) {
        query = query.eq('city', city);
      }

      // Add service category filter if provided
      if (serviceCategories.length > 0) {
        query = query.overlaps('expert_profiles.skills', serviceCategories);
      }

      // Add pagination
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error('Error searching experts by location', {
          error: error.message,
          params
        });
        throw createError.internalServerError('Failed to search experts');
      }

      const experts: ExpertLocationMatch[] = (data || []).map(item => ({
        expertId: item.expert_profiles.id,
        expertProfile: {
          id: item.expert_profiles.id,
          title: item.expert_profiles.title,
          description: item.expert_profiles.description,
          skills: item.expert_profiles.skills,
          rating: item.expert_profiles.rating,
          reviewCount: item.expert_profiles.review_count,
          hourlyRate: item.expert_profiles.hourly_rate,
          responseTime: item.expert_profiles.response_time,
          user: {
            firstName: item.expert_profiles.users.first_name,
            lastName: item.expert_profiles.users.last_name,
            avatar: item.expert_profiles.users.avatar
          }
        },
        serviceArea: {
          id: item.id,
          expertId: item.expert_id,
          governorate: item.governorate,
          city: item.city,
          serviceRadius: item.service_radius,
          travelCost: item.travel_cost,
          isActive: item.is_active,
          createdAt: item.created_at,
          updatedAt: item.updated_at
        }
      }));

      return {
        experts,
        total: count || 0,
        page,
        limit
      };

    } catch (error) {
      logger.error('Error in searchExpertsByLocation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        params
      });
      throw error;
    }
  }

  /**
   * Calculate distance between two Syrian cities (simplified)
   */
  static calculateDistance(
    fromGovernorate: string,
    fromCity: string,
    toGovernorate: string,
    toCity: string
  ): number {
    // Simplified distance calculation for Syrian cities
    // In a real implementation, you would use actual coordinates
    
    if (fromGovernorate === toGovernorate && fromCity === toCity) {
      return 0;
    }
    
    if (fromGovernorate === toGovernorate) {
      return 25; // Same governorate, different city
    }
    
    // Approximate distances between major Syrian governorates (in km)
    const governorateDistances: Record<string, Record<string, number>> = {
      'دمشق': {
        'حلب': 350,
        'حمص': 160,
        'حماة': 210,
        'اللاذقية': 330,
        'طرطوس': 280,
        'درعا': 100,
        'السويداء': 120,
        'القنيطرة': 80
      },
      'حلب': {
        'دمشق': 350,
        'حمص': 190,
        'حماة': 120,
        'اللاذقية': 180,
        'إدلب': 60,
        'الحسكة': 200,
        'الرقة': 160
      }
      // Add more distances as needed
    };
    
    return governorateDistances[fromGovernorate]?.[toGovernorate] || 200; // Default distance
  }

  /**
   * Calculate estimated travel cost
   */
  static calculateTravelCost(distance: number, costPerKm: number = 0.5): number {
    return Math.round(distance * costPerKm * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Remove expert service area
   */
  static async removeExpertServiceArea(expertId: string, areaId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('expert_service_areas')
        .delete()
        .eq('id', areaId)
        .eq('expert_id', expertId);

      if (error) {
        logger.error('Error removing expert service area', {
          expertId,
          areaId,
          error: error.message
        });
        throw createError.internalServerError('Failed to remove service area');
      }

      logger.info('Expert service area removed successfully', {
        expertId,
        areaId
      });

    } catch (error) {
      logger.error('Error in removeExpertServiceArea', {
        expertId,
        areaId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
