"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const database_1 = require("@freela/database");
const logger_1 = require("../utils/logger");
const errors_1 = require("../utils/errors");
class UserService {
    /**
     * Save user onboarding data to database
     */
    static async saveOnboardingData(data) {
        try {
            logger_1.logger.info('Saving user onboarding data to database', {
                userId: data.userId,
                role: data.role,
                location: `${data.city}, ${data.governorate}`
            });
            // Update user profile with onboarding data
            const { error: userError } = await database_1.supabase
                .from('users')
                .update({
                first_name: data.firstName,
                last_name: data.lastName,
                email: data.email,
                phone_number: data.phoneNumber,
                governorate: data.governorate,
                city: data.city,
                role: data.role,
                service_preferences: data.servicePreferences || [],
                project_types: data.projectTypes || [],
                business_info: data.businessInfo || null,
                data_collected: true,
                updated_at: new Date().toISOString()
            })
                .eq('id', data.userId);
            if (userError) {
                logger_1.logger.error('Error updating user profile', {
                    userId: data.userId,
                    error: userError.message
                });
                throw errors_1.createError.internalServerError('Failed to save user data');
            }
            // Create role-specific profile if needed
            if (data.role === 'EXPERT') {
                await this.createExpertProfile(data.userId, data.servicePreferences || []);
            }
            else if (data.role === 'CLIENT') {
                await this.createClientProfile(data.userId);
            }
            logger_1.logger.info('User onboarding data saved successfully', {
                userId: data.userId,
                role: data.role
            });
        }
        catch (error) {
            logger_1.logger.error('Error in saveOnboardingData', {
                userId: data.userId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }
    /**
     * Get user onboarding data
     */
    static async getUserData(userId) {
        try {
            const { data, error } = await database_1.supabase
                .from('users')
                .select(`
          id,
          email,
          phone,
          first_name,
          last_name,
          role,
          governorate,
          city,
          phone_number,
          data_collected,
          has_completed_onboarding,
          service_preferences,
          project_types,
          business_info,
          created_at,
          updated_at
        `)
                .eq('id', userId)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // User not found
                }
                logger_1.logger.error('Error fetching user data', {
                    userId,
                    error: error.message
                });
                throw errors_1.createError.internalServerError('Failed to fetch user data');
            }
            return {
                id: data.id,
                email: data.email,
                phone: data.phone,
                firstName: data.first_name,
                lastName: data.last_name,
                role: data.role,
                governorate: data.governorate,
                city: data.city,
                phoneNumber: data.phone_number,
                dataCollected: data.data_collected,
                servicePreferences: data.service_preferences,
                projectTypes: data.project_types,
                businessInfo: data.business_info,
                hasCompletedOnboarding: data.has_completed_onboarding,
                createdAt: data.created_at,
                updatedAt: data.updated_at
            };
        }
        catch (error) {
            logger_1.logger.error('Error in getUserData', {
                userId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }
    /**
     * Create expert profile
     */
    static async createExpertProfile(userId, skills) {
        try {
            // Check if expert profile already exists
            const { data: existingProfile } = await database_1.supabase
                .from('expert_profiles')
                .select('id')
                .eq('user_id', userId)
                .single();
            if (existingProfile) {
                logger_1.logger.info('Expert profile already exists', { userId });
                return;
            }
            // Create new expert profile
            const { error } = await database_1.supabase
                .from('expert_profiles')
                .insert({
                user_id: userId,
                title: { ar: 'خبير جديد', en: 'New Expert' },
                description: { ar: 'خبير في منصة فريلا سوريا', en: 'Expert on Freela Syria platform' },
                skills: skills,
                experience: 'BEGINNER',
                response_time: 'WITHIN_24_HOURS',
                availability: {
                    timezone: 'Asia/Damascus',
                    workingHours: {
                        monday: { start: '09:00', end: '17:00', available: true },
                        tuesday: { start: '09:00', end: '17:00', available: true },
                        wednesday: { start: '09:00', end: '17:00', available: true },
                        thursday: { start: '09:00', end: '17:00', available: true },
                        friday: { start: '09:00', end: '17:00', available: true },
                        saturday: { start: '09:00', end: '17:00', available: false },
                        sunday: { start: '09:00', end: '17:00', available: false }
                    }
                }
            });
            if (error) {
                logger_1.logger.error('Error creating expert profile', {
                    userId,
                    error: error.message
                });
                throw errors_1.createError.internalServerError('Failed to create expert profile');
            }
            logger_1.logger.info('Expert profile created successfully', { userId });
        }
        catch (error) {
            logger_1.logger.error('Error in createExpertProfile', {
                userId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }
    /**
     * Create client profile
     */
    static async createClientProfile(userId) {
        try {
            // Check if client profile already exists
            const { data: existingProfile } = await database_1.supabase
                .from('client_profiles')
                .select('id')
                .eq('user_id', userId)
                .single();
            if (existingProfile) {
                logger_1.logger.info('Client profile already exists', { userId });
                return;
            }
            // Create new client profile
            const { error } = await database_1.supabase
                .from('client_profiles')
                .insert({
                user_id: userId
            });
            if (error) {
                logger_1.logger.error('Error creating client profile', {
                    userId,
                    error: error.message
                });
                throw errors_1.createError.internalServerError('Failed to create client profile');
            }
            logger_1.logger.info('Client profile created successfully', { userId });
        }
        catch (error) {
            logger_1.logger.error('Error in createClientProfile', {
                userId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }
    /**
     * Update user onboarding completion status
     */
    static async markOnboardingComplete(userId) {
        try {
            const { error } = await database_1.supabase
                .from('users')
                .update({
                has_completed_onboarding: true,
                updated_at: new Date().toISOString()
            })
                .eq('id', userId);
            if (error) {
                logger_1.logger.error('Error marking onboarding complete', {
                    userId,
                    error: error.message
                });
                throw errors_1.createError.internalServerError('Failed to update onboarding status');
            }
            logger_1.logger.info('Onboarding marked as complete', { userId });
        }
        catch (error) {
            logger_1.logger.error('Error in markOnboardingComplete', {
                userId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }
}
exports.UserService = UserService;
//# sourceMappingURL=userService.js.map